require 'rails_helper'

RSpec.describe BadgeIconGroup, type: :model do
  describe "identity image generation trigger" do
    context "when badge icon group attributes change" do
      let(:circle) { FactoryBot.create(:circle) }
      let(:badge_icon_group) { FactoryBot.create(:badge_icon_group, circle: circle) }
      let(:admin_user) { FactoryBot.create(:admin_user) }
      let(:admin_medium) { FactoryBot.create(:admin_medium, blob_data: fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png"), admin_user: admin_user) }
      let(:badge_icon) { FactoryBot.create(:badge_icon, badge_icon_group: badge_icon_group, admin_medium: admin_medium) }
      let(:user) { FactoryBot.create(:user) }
      let(:role) { FactoryBot.create(:role) }
      let!(:user_role) { FactoryBot.create(:user_role, user: user, role: role, badge_icon: badge_icon) }

      it "should trigger identity photo generation worker when name changes" do
        expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id).at_least(:once)

        badge_icon_group.update!(name: "Updated Badge Icon Group Name")
      end

      it "should trigger identity photo generation worker when circle_id changes" do
        new_circle = FactoryBot.create(:circle)

        expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id).at_least(:once)

        badge_icon_group.update!(circle_id: new_circle.id)
      end

      it "should not trigger identity photo generation worker when no relevant attributes change" do
        # Since BadgeIconGroup has limited attributes, we'll test that the method exists
        # and doesn't trigger when there are no changes
        allow(IdentityPhotoGenerationWorker).to receive(:perform_async)
        expect(IdentityPhotoGenerationWorker).not_to receive(:perform_async)

        # Touch the record without changing tracked attributes
        badge_icon_group.touch
      end
    end
  end
end
