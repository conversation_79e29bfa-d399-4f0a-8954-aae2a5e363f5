require 'rails_helper'

RSpec.describe BadgeIcon, type: :model do
  # pending "add some examples to (or delete) #{__FILE__}"

  describe "validate badge icon group id" do
    context "it is valid with numbers only" do
      before :each do
        @photo = FactoryBot.create(:photo)
        @admin_user = FactoryBot.create(:admin_user)
        # data = File.new("app/assets/images/praja-full-logo.png")
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png") #{ |f| @data.store!(f) }
        admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
        @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id)
      end
      it "is not valid without a nil value" do
        @badge_icon.badge_icon_group_id = nil
        expect(@badge_icon).not_to be_valid
      end
    end
  end

  describe "identity image generation trigger" do
    context "when badge icon attributes change" do
      let(:circle) { FactoryBot.create(:circle) }
      let(:badge_icon_group) { FactoryBot.create(:badge_icon_group, circle: circle) }
      let(:admin_user) { FactoryBot.create(:admin_user) }
      let(:admin_medium) { FactoryBot.create(:admin_medium, blob_data: fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png"), admin_user: admin_user) }
      let(:badge_icon) { FactoryBot.create(:badge_icon, badge_icon_group: badge_icon_group, admin_medium: admin_medium) }
      let(:user) { FactoryBot.create(:user) }
      let(:role) { FactoryBot.create(:role) }
      let!(:user_role) { FactoryBot.create(:user_role, user: user, role: role, badge_icon: badge_icon) }

      it "should trigger identity photo generation worker when admin_medium_id changes" do
        new_admin_medium = FactoryBot.create(:admin_medium, blob_data: fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png"), admin_user: admin_user)

        expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id).at_least(:once)

        badge_icon.update!(admin_medium_id: new_admin_medium.id)
      end

      it "should trigger identity photo generation worker when badge_icon_group_id changes" do
        new_circle = FactoryBot.create(:circle)
        new_badge_icon_group = FactoryBot.create(:badge_icon_group, circle: new_circle)

        expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id).at_least(:once)

        badge_icon.update!(badge_icon_group_id: new_badge_icon_group.id)
      end

      it "should not trigger identity photo generation worker when unrelated attributes change" do
        allow(IdentityPhotoGenerationWorker).to receive(:perform_async)
        expect(IdentityPhotoGenerationWorker).not_to receive(:perform_async).with(user.id)

        badge_icon.update!(color: :SILVER)
      end

      it "valid with digits" do
        new_circle = FactoryBot.create(:circle)
        new_badge_icon_group = FactoryBot.create(:badge_icon_group, circle: new_circle)
        badge_icon.badge_icon_group_id = new_badge_icon_group.id
        expect(badge_icon).to be_valid
      end

      it "invalid with not existed group_id" do
        badge_icon.badge_icon_group_id = 1000
        expect(badge_icon).not_to be_valid
      end

      it "not valid with english letters" do
        badge_icon.badge_icon_group_id = "MLA OFFICE"
        expect(badge_icon).not_to be_valid
      end

      it "is not valid with alphanumeric" do
        badge_icon.badge_icon_group_id = "123a"
        expect(badge_icon).not_to be_valid
      end

      it "is not valid with special characters" do
        badge_icon.badge_icon_group_id = "123@"
        expect(badge_icon).not_to be_valid
      end

      it "is not valid with blank" do
        badge_icon.badge_icon_group_id = ""
        expect(badge_icon).not_to be_valid
      end

      it "is not valid with empty space" do
        badge_icon.badge_icon_group_id = " "
        expect(badge_icon).not_to be_valid
      end
    end
  end

  describe "validate badge icon color" do
    before :each do
      @photo = FactoryBot.create(:photo)
      @admin_user = FactoryBot.create(:admin_user)
      # data = File.new("app/assets/images/praja-full-logo.png")
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png") #{ |f| @data.store!(f) }
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
      @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id)
    end
    it "color should be in white,silver,gold" do
      expect(@badge_icon).to be_valid
    end

    it "should not be valid with color nil value" do
      @badge_icon.color = nil
      expect(@badge_icon).not_to be_valid
    end
  end

  describe "validate admin medium id" do
    before :each do
      @photo = FactoryBot.create(:photo)
      @admin_user = FactoryBot.create(:admin_user)
      data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      admin_media = FactoryBot.create(:admin_medium, blob_data: data, admin_user_id: @admin_user.id)
      @badge_icon = FactoryBot.create(:badge_icon, admin_medium_id: admin_media.id)
    end
    context "it is valid with numbers only" do
      it "is not valid without a nil value" do
        @badge_icon.admin_medium_id = nil
        expect(@badge_icon).not_to be_valid
      end

      it "invalid with not existed admin medium id" do
        @badge_icon.admin_medium_id = 1000
        expect(@badge_icon).not_to be_valid
      end

      it "not valid with english letters" do
        @badge_icon.admin_medium_id = "MLA OFFICE"
        expect(@badge_icon).not_to be_valid
      end

      it "is not valid with alphanumeric" do
        @badge_icon.admin_medium_id = "123a"
        expect(@badge_icon).not_to be_valid
      end

      it "is not valid with special characters" do
        @badge_icon.admin_medium_id = "123@"
        expect(@badge_icon).not_to be_valid
      end

      it "is not valid with blank" do
        @badge_icon.admin_medium_id = ""
        expect(@badge_icon).not_to be_valid
      end

      it "is not valid with empty space" do
        @badge_icon.admin_medium_id = " "
        expect(@badge_icon).not_to be_valid
      end
    end
  end
end
