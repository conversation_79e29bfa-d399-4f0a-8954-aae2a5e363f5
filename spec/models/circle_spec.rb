require 'rails_helper'

RSpec.describe Circle, type: :model do

  describe "Validate Short name on circle" do
    context "when short name is not present" do
      it "circle should be valid irrespective of circle_type" do
        @circle = FactoryBot.build(:circle, short_name: nil)
        expect(@circle).to be_valid
        @circle1 = FactoryBot.build(:circle, short_name: "short_name")
        expect(@circle1).to be_valid
      end
    end
    context "when short name is present" do
      it "circle should be valid if circle_type is not location" do
        @circle = FactoryBot.build(:circle, short_name: "short_name")
        expect(@circle).to be_valid
      end
      it "circle should not be valid if circle_type is location" do
        @circle = FactoryBot.build(:circle, circle_type: :location, level: :state, short_name: "short_name")
        expect(@circle).not_to be_valid
        @circle1 = FactoryBot.build(:circle, circle_type: :location, level: :state, short_name: ' ')
        expect(@circle1).to be_valid
      end
    end
  end

  describe "Validate conversation type on circle" do
    context "when conversation type is channel" do
      # TODO: uncomment this test case once we have started enabling channel live
      # it "circle should be a political party" do
      #   circle = FactoryBot.build(:circle, circle_type: :interest, level: :political_party, conversation_type: :channel)
      #   expect(circle).to be_valid
      # end
      #
      # it "circle should be a political leader" do
      #   circle = FactoryBot.build(:circle, circle_type: :interest, level: :political_leader, conversation_type: :channel)
      #   expect(circle).to be_valid
      # end
      #
      # it "circle should not be any other level except political party and political leader" do
      #   circle = FactoryBot.build(:circle, circle_type: :location, level: :state, conversation_type: :channel)
      #   expect(circle).not_to be_valid
      # end
    end
  end

  describe "#get_gradients_for_party_icon" do
    context "get party gradients of a circle" do
      before :each do
        @circle = FactoryBot.create(:circle)
      end
      it "return gradients when party icon position is top and not a pure gold premium layout" do
        gradients_hash = @circle.get_gradients_for_party_icon("top", false)
        expect(gradients_hash).to be_present
        expect(gradients_hash[:colors]).to be_present
        expect(gradients_hash[:colors]).to eq([0xff0061FF, 0xffA1DDFF])
        expect(gradients_hash[:stops]).to be_nil
        expect(gradients_hash[:directions][:begin_x]).to eq(-1.0)
        expect(gradients_hash[:directions][:begin_y]).to eq(-1.0)
        expect(gradients_hash[:directions][:end_x]).to eq(1.0)
        expect(gradients_hash[:directions][:end_y]).to eq(1.0)
      end

      it "return gradients when party icon position is left and a pure gold premium layout" do
        gradients_hash = @circle.get_gradients_for_party_icon("left", true)
        expect(gradients_hash).to be_present
        expect(gradients_hash[:colors]).to be_present
        expect(gradients_hash[:colors]).to eq([0xFFBE6C00, 0xFFCD8E24, 0xFFDFB650, 0xFFECD36F, 0xFFF4E582, 0xFFF7EB89,
                                               0xFFD6AE49, 0xFFBE811A])
        expect(gradients_hash[:stops]).to eq([0.1062, 0.1851, 0.2931, 0.3902, 0.4721, 0.5288, 0.7086, 0.8529])
        expect(gradients_hash[:directions][:begin_x]).to eq(-1.0)
        expect(gradients_hash[:directions][:begin_y]).to eq(1.0)
        expect(gradients_hash[:directions][:end_x]).to eq(1.0)
        expect(gradients_hash[:directions][:end_y]).to eq(-1.0)
      end

      it "return gradients when party icon position is left and not a pure gold premium layout where it has gold
          border" do
        gradients_hash = @circle.get_gradients_for_party_icon("left", true)
        expect(gradients_hash).to be_present
        expect(gradients_hash[:colors]).to be_present
        expect(gradients_hash[:colors]).to eq([0xFFBE6C00, 0xFFCD8E24, 0xFFDFB650, 0xFFECD36F, 0xFFF4E582, 0xFFF7EB89,
                                               0xFFD6AE49, 0xFFBE811A])
        expect(gradients_hash[:stops]).to eq([0.1062, 0.1851, 0.2931, 0.3902, 0.4721, 0.5288, 0.7086, 0.8529])
        expect(gradients_hash[:directions][:begin_x]).to eq(-1.0)
        expect(gradients_hash[:directions][:begin_y]).to eq(1.0)
        expect(gradients_hash[:directions][:end_x]).to eq(1.0)
        expect(gradients_hash[:directions][:end_y]).to eq(-1.0)
      end

      it "return gradients when party icon position is top and not a pure gold premium layout where it has gold
          border" do
        gradients_hash = @circle.get_gradients_for_party_icon("top", true)
        expect(gradients_hash).to be_present
        expect(gradients_hash[:colors]).to be_present
        expect(gradients_hash[:colors]).to eq([0xFFBE6C00, 0xFFCD8E24, 0xFFDFB650, 0xFFECD36F, 0xFFF4E582, 0xFFF7EB89,
                                               0xFFD6AE49, 0xFFBE811A])
        expect(gradients_hash[:stops]).to eq([0.1062, 0.1851, 0.2931, 0.3902, 0.4721, 0.5288, 0.7086, 0.8529])
        expect(gradients_hash[:directions][:begin_x]).to eq(-1.0)
        expect(gradients_hash[:directions][:begin_y]).to eq(1.0)
        expect(gradients_hash[:directions][:end_x]).to eq(1.0)
        expect(gradients_hash[:directions][:end_y]).to eq(-1.0)
      end
    end
  end

  describe "#get_neon_frame_colors" do
    context "get neon frame color of a circle" do
      before :each do
        @circle = FactoryBot.create(:circle)
      end
      it "return neutral neon frame color" do
        neon_frame_color = @circle.get_neon_frame_colors
        expect(neon_frame_color).to be_present
        expect(neon_frame_color).to eq(0xff0266B4)
      end
    end
  end

  describe "#get_last_posted_at" do
    context "get last posted at of a circle" do
      before do
        @circle = FactoryBot.create(:circle)
        @post = FactoryBot.create(:post)
        @post1 = FactoryBot.create(:post)
        FactoryBot.create(:post_circle, post: @post, circle: @circle)
        FactoryBot.create(:post_circle, post: @post1, circle: @circle)
      end

      it "return last posted at" do
        last_posted_at = @circle.get_last_posted_at
        expect(last_posted_at).to be_present
        expect(last_posted_at).to eq(@circle.posts.last.created_at)
      end
    end
  end

  describe "#get_circle_banner" do
    context "get circle banner of a circle" do
      before do
        @photo = FactoryBot.create(:photo)
        @circle = FactoryBot.create(:circle, banner_id: @photo.id)
      end

      it "return circle banner" do
        circle_banner = @circle.get_circle_banner
        expect(circle_banner).to be_present
        expect(circle_banner).to eq(@circle.banner.url)
      end
    end
  end

  describe "#update_members_count" do
    context "update members count of a circle" do
      before do
        @circle = FactoryBot.create(:circle)
      end

      it "return members count" do
        expect(@circle.members_count).to eq(0)
        FactoryBot.create(:user_circle, circle: @circle)
        @circle.update_members_count
        expect(@circle.members_count).to eq(1)
      end
    end
  end

  describe "#get_members_count" do
    context "get members count of a circle" do
      before do
        @circle = FactoryBot.create(:circle)
      end

      it "return members count" do
        @circle.members_count = 1
        expect(@circle.get_members_count).to eq(1)
      end

      it "updates members count of child circles for mandal and district" do
        @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)

        FactoryBot.create(:user_circle, circle: @village)
        FactoryBot.create(:user_circle, circle: @village)
        FactoryBot.create(:user_circle, circle: @village)

        expect(@district.get_members_count).to eq(3)
        expect(@mandal.get_members_count).to eq(3)
      end
    end
  end

  describe "#get_badge_users_count" do
    context "get badge users count of a circle" do
      before :each do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user)
        @user2 = FactoryBot.create(:user)
        @user3 = FactoryBot.create(:user)
        FactoryBot.create(:user_circle, circle: @circle, user: @user)
        FactoryBot.create(:user_circle, circle: @circle, user: @user2)
        FactoryBot.create(:user_circle, circle: @circle, user: @user3)
        @user_role = FactoryBot.create(:user_role, user: @user)
        @user_role2 = FactoryBot.create(:user_role, user: @user2)
        @user_role3 = FactoryBot.create(:user_role, user: @user3)
      end

      it "return badge users count" do
        expect(@circle.get_badge_users_count).to eq(3)
      end
    end
  end

  describe "#get_last_parent" do
    context "get last parent of a circle" do
      before :each do
        @circle = FactoryBot.create(:circle)
        @circle2 = FactoryBot.create(:circle, parent_circle: @circle)
        @circle3 = FactoryBot.create(:circle, parent_circle: @circle2)
      end

      it "return last parent" do
        expect(@circle3.get_last_parent).to eq(@circle)
      end
    end
  end

  describe "#get_owner_name" do
    context "get owner name of a circle" do
      before :each do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group_id: Constants.owner_permission_group_id)
      end

      it "return owner name" do
        expect(@circle.get_owner&.name).to eq(@user.name)
      end
    end
  end

  describe "#official?" do
    context "check whether the circle is official or verified" do
      it "returns false if circle is not official" do
        @circle = FactoryBot.create(:circle)
        expect(@circle.official?).to eq(false)
      end

      it "returns true if circle is official" do
        @circle = FactoryBot.create(:circle, conversation_type: :channel)
        expect(@circle.official?).to eq(true)
      end
    end

  end

  describe "#level_verbose" do
    context "get level verbose of a circle" do
      it "returns short info of the circle as level verbose" do
        @circle = FactoryBot.create(:circle, level: :political_leader, short_info: "info",
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        expect(@circle.level_verbose).to eq(@circle.short_info)
      end

      it "returns political leader  with his contituency as level verbose" do
        @circle = FactoryBot.create(:circle, level: :political_leader, circle_type: :interest,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @mp = FactoryBot.create(:circle, level: :mp_constituency, circle_type: :location)

        @user = FactoryBot.create(:user)
        @circles_relation = FactoryBot.create(:circles_relation, first_circle: @mp, second_circle: @circle, relation: "MP")

        expect(@circle.level_verbose).to eq("#{@circles_relation.relation} @ #{@circles_relation.first_circle.name}")
      end

      it "returns the level name in telugu as level verbose if circle level is other than political leader" do
        @circle = FactoryBot.create(:circle)
        expect(@circle.level_verbose).to eq('రాజకీయ పార్టీ')
      end
    end
  end

  describe "#get_new_posts_count" do
    context "get new posts count of a circle" do
      before :each do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user)
      end

      it "returns new posts count of a circle if user opened time greater than 0" do
        @post = FactoryBot.create(:post)
        @post2 = FactoryBot.create(:post)
        @post3 = FactoryBot.create(:post)
        @post4 = FactoryBot.create(:post)

        FactoryBot.create(:post_circle, post: @post, circle: @circle)
        FactoryBot.create(:post_circle, post: @post2, circle: @circle)
        FactoryBot.create(:post_circle, post: @post3, circle: @circle)
        FactoryBot.create(:post_circle, post: @post4, circle: @circle)

        allow($redis).to receive(:hget).with("circle_last_open_#{@circle.id}", @user.id.to_s).and_return(10.minutes.ago.to_i.to_s)
        expect(@circle.get_new_posts_count(@user)).to eq(4)
      end

      it "returns new posts count of a circle if user opened time less than or equal to 0" do
        @post = FactoryBot.create(:post)
        FactoryBot.create(:post_circle, post: @post, circle: @circle)

        allow($redis).to receive(:hget).with("circle_last_open_#{@circle.id}", @user.id.to_s).and_return("0")
        expect(@circle.get_new_posts_count(@user)).to eq(0)
      end
    end
  end

  describe "#get_poster" do
    context "get poster of a circle" do
      it "returns poster of a circle if app version is less than 1.16.0" do
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user)
        @poster = FactoryBot.create(:poster, circle: @circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        app_version = Gem::Version.new("1.15.0")

        allow(@poster).to receive(:get_poster_hash).with(@user, app_version).and_return({ id: @poster.id, leaders_photo_urls: [] })
        get_poster = @circle.get_poster(@user, app_version)
        expect(get_poster).to be_present
        expect(get_poster[:id]).to eq(@poster.id)
        expect(get_poster[:leaders_photo_urls]).to eq([])
      end

      it "returns poster of party circle of party user if app version is greater than or equal to 1.16.0" do
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
        @circle = FactoryBot.create(:circle)
        @circle_photo = FactoryBot.create(:circle_photo, circle: @circle)
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @poster = FactoryBot.create(:poster, circle: @circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        app_version = Gem::Version.new("1.16.1")

        get_poster = @circle.get_poster(@user, app_version)
        expect(get_poster).to be_present
        expect(get_poster[:id]).to eq(@poster.id)
        expect(get_poster[:leaders_photo_urls].count).to eq(1)
      end

      it "returns poster of party circle of party user if app version is greater than or equal to 1.16.0" do
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
        @circle = FactoryBot.create(:circle)
        @circle2 = FactoryBot.create(:circle)
        @circle_photo = FactoryBot.create(:circle_photo, circle: @circle)
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle2.id)
        @poster = FactoryBot.create(:poster, circle: @circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        app_version = Gem::Version.new("1.16.1")

        get_poster = @circle.get_poster(@user, app_version)
        expect(get_poster).not_to be_present
      end

      it "returns poster of leader circle if app version is greater than or equal to 1.16.0" do
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
        @party = FactoryBot.create(:circle)
        FactoryBot.create(:circle_photo, circle: @party)
        @circle = FactoryBot.create(:circle, level: :political_leader,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @circle_photo = FactoryBot.create(:circle_photo, circle: @circle)
        @circle_relation = FactoryBot.create(:circles_relation, first_circle: @circle, second_circle: @party, relation: 'Leader2Party')
        @user = FactoryBot.create(:user)
        @poster = FactoryBot.create(:poster, circle: @circle, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        app_version = Gem::Version.new("1.16.1")

        get_poster = @circle.get_poster(@user, app_version)
        expect(get_poster).to be_present
        expect(get_poster[:id]).to eq(@poster.id)
        expect(get_poster[:leaders_photo_urls].count).to eq(1)
      end

      it "returns poster of village if user belongs to user if greater than or equal to 1.16.0" do
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
        @state = FactoryBot.create(:circle, level: :state, circle_type: :location)
        @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
        @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
        @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
        @user = FactoryBot.create(:user, village_id: @village.id)
        @poster = FactoryBot.create(:poster, circle: @village, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        app_version = Gem::Version.new("1.16.1")

        get_poster = @village.get_poster(@user, app_version)
        expect(get_poster).to be_present
        expect(get_poster[:id]).to eq(@poster.id)
      end
    end
  end

  describe "#get_badge_card_data" do
    context "if eligible for badge card" do
      it "returns badge card data" do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)
        @user_role = FactoryBot.create(:user_role, user: @user, parent_circle_id: @circle.id)
        allow(@user).to receive(:is_eligible_for_badge_card).and_return([true, @user_role.parent_circle_id])
        allow(Role).to receive(:find).with(Constants.get_vip_status_badge_role_id).and_return(@user_role.role)

        badge_card_data = @circle.get_badge_card_data(@user, Gem::Version.new("1.16.1"))
        expect(badge_card_data).to be_present
        expect(badge_card_data[:feed_type]).to eq("badge_card")
        expect(badge_card_data[:badge][:id]).to eq(@user_role.role.id)
      end
    end

    context "if not eligible for badge card" do
      it "returns nil" do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user, affiliated_party_circle_id: @circle.id)

        badge_card_data = @circle.get_badge_card_data(@user, Gem::Version.new("1.16.1"))
        expect(badge_card_data).not_to be_present
      end
    end
  end

  describe "#get_active_posters" do
    context "if village has no active posters" do
      it "returns active posters of a parent circles of the village" do
        state = FactoryBot.create(:circle, level: :state, circle_type: :location)
        district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
        mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
        village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
        user = FactoryBot.create(:user, village_id: village.id)
        normal_poster_photo = fixture_file_upload("app/assets/images/poster.jpg", "image/jpg")
        poster = FactoryBot.create(:poster, circle: mandal, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        poster2 = FactoryBot.create(:poster, circle: mandal, poster_photos: [FactoryBot.build(:poster_photo, blob_data: normal_poster_photo, photo: FactoryBot.build(:admin_medium, blob_data: normal_poster_photo))])
        app_version = Gem::Version.new("1.16.1")

        get_posters = village.get_active_posters(user, app_version)
        expect(get_posters.count).to eq(2)
        expect(get_posters.first[:id]).to eq(poster2.id)
      end
    end
  end

  describe "#intro" do
    context "if political_party" do
      it "returns intro of political party" do
        @circle = FactoryBot.create(:circle)

        get_intro = @circle.intro
        expect(get_intro).to be_present
        expect(get_intro).to eq("#{@circle.name} లేటెస్ట్ అప్డేట్స్ కొరకు, మరియు పార్టీ సభ్యులతో కనెక్ట్ అవటం కొరకు జాయిన్ అవ్వండి!")
      end
    end
    context "if political leader" do
      it "returns intro of political leader" do
        @mp = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        @circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        @circle_relation = FactoryBot.create(:circles_relation, first_circle: @mp, second_circle: @circle, relation: 'MP')

        get_intro = @circle.intro
        expect(get_intro).to be_present
        intro = "#{@circle_relation.relation}, #{@circle_relation.first_circle.name}\n" +
                "#{@circle.name} లేటెస్ట్ అప్డేట్స్ కొరకు, మరియు వారి ఫాలోవర్స్ తో కనెక్ట్ అవ్వటం కొరకు జాయిన్ అవ్వండి!"
        expect(get_intro).to eq(intro)
      end
    end
  end

  describe "#get_all_parent_circles" do
    before :each do
      @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
      @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
      @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
      @municipality = FactoryBot.create(:circle, circle_type: :location, level: :municipality, parent_circle: @mandal)
      @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
    end
    context 'when parent_circle is nil' do
      it 'returns an empty array' do
        result = @state.get_all_parent_circles
        expect(result).to eq([])
      end
    end

    context 'when parent_circle is present' do
      it 'returns an array of parent_circles' do
        result = @village.get_all_parent_circles
        expect(result).to eq([@mandal, @district, @state])
      end

      it 'returns an array of parent_circles ignore mandal being true' do
        result = @village.get_all_parent_circles(true)
        expect(result).to eq([@district, @state])
      end
    end
  end

  describe "get_all_parent_circle_ids" do
    before :each do
      @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
      @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
      @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
      @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
    end
    context 'when parent_circle is nil' do
      it 'returns an empty array' do
        result = @state.get_all_parent_circle_ids
        expect(result).to eq([])
      end
    end

    context 'when parent_circle is present' do
      it 'returns an array of parent_circle ids' do
        result = @village.get_all_parent_circle_ids
        expect(result).to eq([@mandal.id, @district.id, @state.id])
      end
    end
  end

  describe '#get_all_child_circles_including_itself & #get_all_child_circles' do
    before :each do
      @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
      @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
      @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
      @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
    end
    context 'when circle has no children' do
      it 'returns an array with only itself' do
        result = @village.get_all_child_circles_including_itself
        expect(result).to eq([@village])
      end

      it 'returns an empty array' do
        result = @village.get_all_child_circles
        expect(result).to eq([])
      end
    end

    context 'when circle has children' do
      it 'returns an array of child circles including itself' do
        result = @state.get_all_child_circles_including_itself
        expect(result).to eq([@state, @district, @mandal, @village])
      end

      it 'returns an array of child circles excluding itself' do
        result = @state.get_all_child_circles
        expect(result).to eq([@district, @mandal, @village])
      end
    end
  end

  describe '#get_all_village_circles' do
    context 'get all village circles of a circle' do
      it 'returns an empty array' do
        state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        result = state.get_all_village_circles
        expect(result).to eq([state])
      end

      it 'returns an array of village circles' do
        state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
        mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
        mandal2 = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
        village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal)
        village2 = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: mandal2)

        result = state.get_all_village_circles
        expect(result).to eq([village, village2])

        result = village.get_all_village_circles
        expect(result).to eq([village])
      end
    end
  end

  describe '#get_posts' do
    context 'get posts of a circle' do
      it 'returns an empty array' do
        circle = FactoryBot.create(:circle)
        user = FactoryBot.create(:user)

        result = circle.get_posts(user, 0, 10, Gem::Version.new('1.13.2'))
        expect(result).to eq([])
      end

      it 'returns an array of posts' do
        circle = FactoryBot.create(:circle)
        user = FactoryBot.create(:user)
        post = FactoryBot.create(:post, user: user)
        FactoryBot.create(:post_circle, post: post, circle: circle)
        result = circle.get_posts(post.user, 0, 10, Gem::Version.new('1.13.2'))
        expect(result).to eq([post])
      end
    end
  end

  describe '#mark_suggested_as_seen' do
    context 'is user sees the suggested circle' do
      it 'marks the suggested circle as seen' do
        user = FactoryBot.create(:user)
        circle = FactoryBot.create(:circle)

        allow($redis).to receive(:hexists).with("suggested_circle_views_#{circle.id}", user.id.to_s).and_return(false)
        allow($redis).to receive(:hset).with("suggested_circle_views_#{circle.id}", user.id.to_s, Time.now.to_i.to_s)
        circle.mark_suggested_as_seen(user.id)
        expect($redis).to have_received(:hset).with("suggested_circle_views_#{circle.id}", user.id.to_s, Time.now.to_i.to_s)
      end
    end
  end

  describe '#get_circle_affiliated_party_id' do
    context 'get leader circle party id' do
      it 'returns circle party id' do
        circle = FactoryBot.create(:circle, level: :political_leader,
                                   circle_photos:
                                     [FactoryBot.build(:circle_photo,
                                                       photo: FactoryBot.create(:photo),
                                                       photo_type: :poster,
                                                       photo_order: 1)])
        party = FactoryBot.create(:circle)
        FactoryBot.create(:circles_relation, first_circle: circle, second_circle: party, relation: 'Leader2Party')
        result = circle.get_leader_circle_affiliated_party_id
        expect(result).to eq(result)
      end
    end
  end

  describe '#search_entity_obj' do
    context 'if party circle' do
      it 'reuturns circle object' do
        party = FactoryBot.create(:circle, level: :political_party)
        state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        FactoryBot.create(:circles_relation, first_circle: party, second_circle: state, relation: 'Party2State')
        result = party.search_entity_obj
        expect(result[:state_id]).to eq(state.id)
        expect(result[:has_poster_photo]).to be_falsey
      end
    end

    context 'if leader circle has mp constituency relation' do
      it 'returns circle object' do
        leader = FactoryBot.create(:circle, level: :political_leader,
                                   circle_photos:
                                     [FactoryBot.build(:circle_photo,
                                                       photo: FactoryBot.create(:photo),
                                                       photo_type: :poster,
                                                       photo_order: 1)])
        mp = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        FactoryBot.create(:circles_relation, first_circle: mp, second_circle: leader, relation: 'MP')
        result = leader.search_entity_obj
        expect(result[:mp_constituency_id]).to eq(mp.id)
        expect(result[:photo_id]).to eq(0)
        expect(result[:has_poster_photo]).to be_truthy
      end
    end

    context 'if leader circle has mla constituency relation' do
      it 'returns circle object' do
        leader = FactoryBot.create(:circle, level: :political_leader,
                                   photo: FactoryBot.create(:photo),
                                   circle_photos:
                                     [FactoryBot.build(:circle_photo,
                                                       photo_type: :poster,
                                                       photo_order: 1),
                                      FactoryBot.build(:circle_photo,
                                                       photo_type: :poster,
                                                       photo_order: 2)
                                     ])
        mp = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
        mla = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency, parent_circle_id: mp.id)
        state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle_id: state.id)
        mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle_id: district.id)

        FactoryBot.create(:circles_relation, first_circle: mandal, second_circle: mla, relation: 'Mandal2MLA')
        FactoryBot.create(:circles_relation, first_circle: mla, second_circle: leader, relation: 'MLA')

        result = leader.search_entity_obj
        expect(result).to be_present
        expect(result[:name]).to eq(leader.name)
        expect(result[:mla_constituency_id]).to eq(mla.id)
        expect(result[:mp_constituency_id]).to eq(mp.id)
        expect(result[:state_id]).to eq(state.id)
        expect(result[:district_id]).to eq(district.id)
        expect(result[:mandal_id]).to eq(0)
        expect(result[:village_id]).to eq(0)
        expect(result[:photo_id]).to eq(leader.photo_id)
        expect(result[:has_poster_photo]).to be_truthy
      end
    end
  end

  describe 'validate circle' do
    context 'when parent_circle is nil' do
      it "it doesn't validate circle" do
        circle = FactoryBot.build(:circle, circle_type: :location, level: :village, parent_circle: nil)
        expect(circle.valid?).to eq(false)
      end
    end

    context 'when parent_circle is present' do
      it 'validates circle' do
        state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: state)
        mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: district)
        circle = FactoryBot.build(:circle, circle_type: :location, level: :village, parent_circle: mandal)
        expect(circle.valid?).to eq(true)
      end
    end
  end

  describe '#check_quota_limit_of_role_in_circle' do
    context 'if role has absolute quota type' do
      it 'when quota limit is not exceeded returns true' do
        circle = FactoryBot.create(:circle)
        role = FactoryBot.create(:role)
        FactoryBot.create(:user_role, role: role, parent_circle_id: circle.id)
        expect(circle.check_quota_limit_of_role_in_circle(role.id)).to eq(true)
      end

      it 'when quota limit is exceeded returns false' do
        circle = FactoryBot.create(:circle)
        role = FactoryBot.create(:role)
        FactoryBot.create(:user_role, role: role, parent_circle_id: circle.id)
        FactoryBot.create(:user_role, role: role, parent_circle_id: circle.id)
        expect(circle.check_quota_limit_of_role_in_circle(role.id)).to eq(false)
      end
    end

    context 'if role has percentage quota type' do
      it 'when quota limit is not exceeded returns true' do
        circle = FactoryBot.create(:circle, members_count: 2)
        role = FactoryBot.create(:role, quota_type: :percentage, quota_value: 50)
        expect(circle.check_quota_limit_of_role_in_circle(role.id)).to eq(true)
      end

      it 'when quota limit is exceeded returns false' do
        circle = FactoryBot.create(:circle, members_count: 2)
        role = FactoryBot.create(:role, quota_type: :percentage, quota_value: 50)
        FactoryBot.create(:user_role, role: role, parent_circle_id: circle.id)
        expect(circle.check_quota_limit_of_role_in_circle(role.id)).to eq(false)
      end
    end
  end

  describe '#get_location_circle_data' do
    let(:state) { FactoryBot.create(:circle, circle_type: :location, level: :state) }
    let(:district) { FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle_id: state.id) }
    let(:mandal) { FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle_id: district.id) }
    let(:village) { FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle_id: mandal.id) }
    let(:mp) { FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency) }
    let(:mla) { FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency, parent_circle_id: mp.id) }
    context 'if circle is location' do
      it 'is state returns data of state' do
        circle_data = state.get_location_circle_data
        expect(circle_data).to be_present
        expect(circle_data["state_id"]).to eq(state.id)
      end

      it 'is district returns data of district' do
        circle_data = district.get_location_circle_data
        expect(circle_data).to be_present
        expect(circle_data["state_id"]).to eq(state.id)
        expect(circle_data["district_id"]).to eq(district.id)
      end

      it 'is mandal returns data of mandal' do
        circle_data = mandal.get_location_circle_data
        expect(circle_data).to be_present
        expect(circle_data["state_id"]).to eq(state.id)
        expect(circle_data["district_id"]).to eq(district.id)
      end

      it 'is mp_constituency returns data of mp_constituency' do
        FactoryBot.create(:circles_relation, first_circle: mandal, second_circle: mla, relation: 'Mandal2MLA')
        circle_data = mp.get_location_circle_data
        expect(circle_data).to be_present
        expect(circle_data["state_id"]).to eq(state.id)
        expect(circle_data["mp_constituency_id"]).to eq(mp.id)
      end

      it 'is mla_constituency returns data of mla_constituency' do
        FactoryBot.create(:circles_relation, first_circle: mandal, second_circle: mla, relation: 'Mandal2MLA')
        circle_data = mla.get_location_circle_data
        expect(circle_data).to be_present
        expect(circle_data["state_id"]).to eq(state.id)
        expect(circle_data["district_id"]).to eq(district.id)
        expect(circle_data["mp_constituency_id"]).to eq(mp.id)
        expect(circle_data["mla_constituency_id"]).to eq(mla.id)
      end

      it 'is village returns data of village' do
        FactoryBot.create(:circles_relation, first_circle: mandal, second_circle: mla, relation: 'Mandal2MLA')

        circle_data = village.get_location_circle_data
        expect(circle_data).to be_present
        expect(circle_data["state_id"]).to eq(state.id)
        expect(circle_data["district_id"]).to eq(district.id)
        expect(circle_data["mp_constituency_id"]).to eq(mp.id)
        expect(circle_data["mla_constituency_id"]).to eq(mla.id)
        expect(circle_data["mandal_id"]).to eq(mandal.id)
        expect(circle_data["village_id"]).to eq(village.id)
      end
    end

    context 'if circle is not location' do
      it 'returns nil' do
        circle = FactoryBot.create(:circle)
        circle_data = circle.get_location_circle_data
        expect(circle_data).not_to be_present
      end
    end
  end

  describe '#get_admin_options' do
    context 'if app version' do
      it ' is less than 1.16.3 returns nil' do
        circle = FactoryBot.create(:circle)
        post = FactoryBot.create(:post)
        user = FactoryBot.create(:user)
        admin_options = described_class.get_admin_options(user, post.id, [circle.id], Gem::Version.new('1.16.2'))
        expect(admin_options).to eq(nil)
      end

      it ' is greater than 1.16.3 returns admin options' do
        circle = FactoryBot.create(:circle)
        post = FactoryBot.create(:post)
        user = FactoryBot.create(:user)

        pg = FactoryBot.create(:permission_group, permission_group_permissions: [FactoryBot.build(:permission_group_permission, permission_identifier: :remove_tag)])
        FactoryBot.create(:user_circle_permission_group, user: user, circle: circle, permission_group: pg)
        admin_options = described_class.get_admin_options(user, post.id, [circle.id], Gem::Version.new('1.16.4'))
        expect(admin_options).to be_present
        expect(admin_options[:header_text]).to eq("అడ్మిన్ ఆప్షన్స్")
        expect(admin_options[:options]).to be_present
        expect(admin_options[:options].count).to eq(1)
        expect(admin_options[:options][0][:option_text]).to eq("పోస్ట్ నుండి సర్కిల్ ట్యాగ్‌ని తీసివేయండి")
      end
    end
  end

  describe '#get_state_ids' do
    context 'get the existing state ids' do
      it 'returns state ids' do
        state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        state2 = FactoryBot.create(:circle, circle_type: :location, level: :state)
        state_ids = described_class.get_state_ids
        expect(state_ids).to be_present
        expect(state_ids.count).to eq(2)
        expect(state_ids[0]).to eq(state.id)
        expect(state_ids[1]).to eq(state2.id)
      end
    end
  end

  describe 'check circle upload creative' do
    context 'check circle upload creative' do
      let(:s3_resource_mock) { instance_double(Aws::S3::Resource) }
      let(:s3_bucket_mock) { instance_double(Aws::S3::Bucket) }
      let(:s3_object_mock) { instance_double(Aws::S3::Object) }
      let(:url) { Faker::Internet.url }
      let(:uri) { URI.parse(url) }
      let(:response) { double("response") }

      it 'returns true if the user has circle upload creative' do
        user = FactoryBot.create(:user)
        circle = FactoryBot.create(:circle)

        allow_any_instance_of(PosterCreative).to receive(:check_photos_aspect_ratio).and_return(true)
        allow_any_instance_of(PosterCreative).to receive(:presence_of_photo_v3).and_return(true)
        allow_any_instance_of(PosterCreative).to receive(:validate_whether_event_has_primary_creative).and_return(true)

        photo = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")

        allow(Rails.application.credentials).to receive(:[]).with(:aws_s3_bucket_name).and_return("test-bucket")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_access_key_id).and_return("test-access-key")
        allow(Rails.application.credentials).to receive(:[]).with(:aws_secret_access_key).and_return("test-secret-access-key")

        allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource_mock)

        allow(URI).to receive(:parse).and_return(uri)
        allow(Net::HTTP).to receive(:get_response).with(uri).and_return(response)
        allow(response).to receive(:code).and_return(200)

        allow_any_instance_of(Kernel).to receive(:open).with(url).and_return("test-image")
        allow(IO).to receive(:copy_stream).and_return(true)
        allow(Digest::MD5).to receive(:hexdigest).and_return("test-md5-hash.jpg")

        allow(s3_resource_mock).to receive(:bucket).and_return(s3_bucket_mock)
        allow(s3_bucket_mock).to receive(:object).and_return(s3_object_mock)
        allow(s3_object_mock).to receive(:put).and_return(true)

        poster_creative = circle.upload_creative(photo, user)
        expect(poster_creative).to be_present
        expect(poster_creative.id).to be_present
        expect(poster_creative.creative_kind).to eq('info')
      end
    end
  end

  describe '#get_state_level_leader_ids' do
    context 'get the state level leader ids' do
      it 'return state level leader ids' do
        state = FactoryBot.create(:circle, circle_type: :location, level: :state)
        leader1 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        leader2 = FactoryBot.create(:circle, circle_type: :interest, level: :political_leader,
                                    circle_photos:
                                      [FactoryBot.build(:circle_photo,
                                                        photo: FactoryBot.create(:photo),
                                                        photo_type: :poster,
                                                        photo_order: 1)])
        FactoryBot.create(:circles_relation, first_circle: leader1, second_circle: state, relation: 'Leader2State')
        FactoryBot.create(:circles_relation, first_circle: leader2, second_circle: state, relation: 'Leader2State')
        expect(described_class.get_state_level_leader_ids).to eq([leader1.id, leader2.id])
      end
    end
  end

  describe '#get_users' do
    context 'if the circle is public circle get the users' do
      let(:circle) { Circle.find(0) }
      let(:logged_in_user) { FactoryBot.create(:user) }
      let(:user1) { FactoryBot.create(:user) }
      let(:user2) { FactoryBot.create(:user) }
      let(:user3) { FactoryBot.create(:user) }

      it 'including logged in user if offset is 0' do
        FactoryBot.create(:user_follower, user: user1, follower: logged_in_user)
        FactoryBot.create(:user_follower, user: user2, follower: logged_in_user)
        expect(circle.get_users(logged_in_user, 0, 10)).to eq([logged_in_user, user1, user2])
      end

      it 'excluding logged in user if offset > 0' do
        FactoryBot.create(:user_follower, user: user1, follower: logged_in_user)
        FactoryBot.create(:user_follower, user: user2, follower: logged_in_user)
        FactoryBot.create(:user_follower, user: user3, follower: logged_in_user)
        expect(circle.get_users(logged_in_user, 1, 2)).to eq([user2, user3])
      end
    end

    context "if the circle is other than public circle get the users of all it's child circles including itself" do
      let(:circle) { FactoryBot.create(:circle) }
      let(:logged_in_user) { FactoryBot.create(:user) }
      let(:user1) { FactoryBot.create(:user) }
      let(:user2) { FactoryBot.create(:user) }
      let(:user3) { FactoryBot.create(:user) }

      it "returns users of the circle" do
        FactoryBot.create(:user_circle, user: user1, circle: circle)
        FactoryBot.create(:user_circle, user: user2, circle: circle)
        FactoryBot.create(:user_circle, user: user3, circle: circle)
        FactoryBot.create(:user_follower, user: user1, follower: logged_in_user)
        FactoryBot.create(:user_follower, user: user2, follower: logged_in_user)
        FactoryBot.create(:user_follower, user: user3, follower: logged_in_user)
        expect(circle.get_users(logged_in_user, 0, 10)).to eq([user1, user2, user3])
      end

      it "returns users of the circle and it's child circles" do
        child_circle = FactoryBot.create(:circle, parent_circle: circle)
        FactoryBot.create(:user_circle, user: user1, circle: circle)
        FactoryBot.create(:user_circle, user: user2, circle: child_circle)
        FactoryBot.create(:user_circle, user: user3, circle: child_circle)
        FactoryBot.create(:user_follower, user: user1, follower: logged_in_user)
        FactoryBot.create(:user_follower, user: user2, follower: logged_in_user)
        FactoryBot.create(:user_follower, user: user3, follower: logged_in_user)
        expect(circle.get_users(logged_in_user, 0, 10)).to eq([user1, user2, user3])
      end
    end
  end

  describe "if conversation type has changed" do
    context "to channel it should make a callback to dm service on before save" do
      # need to stub dm service every time when changing conversation type as it make a before save callback and updates
      # the conversation type only if the request gets success
      # to make conversation type as channel we need to make a post request to dm service
      # allow(DmUtil).to receive(:post_request_to_dm).and_return(OpenStruct.new(code: 201, body: { success: true }.to_json))
      # to make conversation type as none we need to make a put request to dm service
      # allow(DmUtil).to receive(:put_request_to_dm).and_return(OpenStruct.new(code: 200, body: { success: true }.to_json))
      it "on success response updates conversation type to channel" do
        allow(DmUtil).to receive(:create_channel_callback_to_dm).and_return(true)
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
        circle.conversation_type = :channel
        circle.save!
        expect(circle.conversation_type).to eq("channel")

        # an unlimited circle package mapping willl be created for the circle
        # need to delete it
        circle.active_circle_package_mapping.destroy
        allow(DmUtil).to receive(:disable_channel_callback_to_dm).and_return(true)
        circle.conversation_type = :none
        circle.save!
        expect(circle.conversation_type).to eq("none")

        circle.conversation_type = :channel
        circle.save!
        expect(circle.conversation_type).to eq("channel")
      end

      it "on success response updates conversation type to private group" do
        allow(DmUtil).to receive(:create_private_group_callback_to_dm).and_return(true)
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :private_group)
        expect(circle.conversation_type).to eq("private_group")

        allow(DmUtil).to receive(:disable_channel_callback_to_dm).and_return(true)
        circle.conversation_type = :none
        circle.save!
        expect(circle.conversation_type).to eq("none")

        circle.conversation_type = :private_group
        circle.save!
        expect(circle.conversation_type).to eq("private_group")
      end

      it "on failure response it does not update the conversation type and sets none" do
        allow(DmUtil).to receive(:create_channel_callback_to_dm).and_return(false)
        expect {
          circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
          circle.conversation_type = :channel
          circle.save!
        }.to raise_error(ActiveRecord::RecordNotSaved)
      end

      it "on failure response it does not update the conversation type and sets old conversation type" do
        allow(DmUtil).to receive(:create_channel_callback_to_dm).and_return(true)
        circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private, conversation_type: :channel)
        allow(DmUtil).to receive(:disable_circle_conversations_callback_to_dm).and_return(false)
        circle.conversation_type = :none
        expect { circle.save! }.to raise_error(ActiveRecord::RecordNotSaved)
        expect(circle.conversation_type).to eq("channel")
      end
    end
  end

  describe 'sub circle creation' do
    context 'validate parent circle to be present for the sub circle creation' do
      it 'returns error if parent circle is not present' do
        circle = FactoryBot.build(:circle, circle_type: :sub, level: :sub, parent_circle: nil)
        expect(circle.valid?).to eq(false)
        expect(circle.errors.full_messages).to eq(["Parent circle can't be blank"])
      end
      it 'should create sub circle if parent circle is present' do
        parent_circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
        circle = FactoryBot.build(:circle, circle_type: :sub, level: :sub, parent_circle: parent_circle)
        expect(circle.valid?).to eq(true)
      end
    end

    context 'validate type and level for sub circle while creation' do

      it 'should be valid if type and level of sub circle is sub' do
        parent_circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
        circle = FactoryBot.build(:circle, circle_type: :sub, level: :sub, parent_circle: parent_circle)
        expect(circle.valid?).to eq(true)
      end
      it 'should throw error if type is not sub when the level is sub' do
        parent_circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
        circle = FactoryBot.build(:circle, circle_type: :my_circle, level: :sub, parent_circle: parent_circle)
        expect(circle.valid?).to eq(false)
      end
      it 'should throw error if level is not sub when the type is sub' do
        parent_circle = FactoryBot.create(:circle, circle_type: :my_circle, level: :private)
        circle = FactoryBot.build(:circle, circle_type: :sub, level: :private, parent_circle: parent_circle)
        expect(circle.valid?).to eq(false)
      end
    end
  end

  describe '#update_conversation_type_to_none' do
    context 'if nil before save' do
      it 'updates conversation type to none' do
        circle = FactoryBot.create(:circle, conversation_type: nil)
        expect(circle.conversation_type).to eq("none")
      end
    end

    context 'if not nil before save' do
      it 'does not update conversation type to none' do
        allow(DmUtil).to receive(:create_channel_callback_to_dm).and_return(true)
        allow(DmUtil).to receive(:disable_channel_callback_to_dm).and_return(true)
        circle = FactoryBot.create(:circle, conversation_type: :channel)
        expect(circle.conversation_type).to eq("channel")

        circle = FactoryBot.create(:circle, conversation_type: :none)
        expect(circle.conversation_type).to eq("none")
        circle.conversation_type = :channel
        circle.save!
        expect(circle.conversation_type).to eq("channel")
      end
    end
  end

  describe '#sponsorship_json' do
    context 'sponsorship json of a circle' do
      it 'returns sponsorship json of a circle' do
        circle = FactoryBot.create(:circle, photo: FactoryBot.create(:photo))
        user = FactoryBot.create(:user)
        expect(circle.sponsorship_json).to eq({ icon_url: circle.photo.url,
                                                line_1: 'స్పాన్సర్',
                                                line_2: circle.name,
                                                line_2_text_color: circle.get_text_color,
                                                gradients: circle.badge_banner_gradients_v2 })
      end
    end
  end

  describe "#add_or_remove_user_as_admin" do
    context "get owner name of a circle" do
      before :each do
        @circle = FactoryBot.create(:circle)
        @user = FactoryBot.create(:user)
        @permission_group = FactoryBot.build(:permission_group, name: 'admin')
        @permission_group.save(validate: false)
        @permission_group_permission = FactoryBot.create(:permission_group_permission,
                                                         permission_group_id: @permission_group.id,
                                                         permission_identifier: :add_tag)
        @user_circle_permission_group = FactoryBot.create(:user_circle_permission_group, user: @user, circle: @circle, permission_group_id: Constants.owner_permission_group_id)
      end

      it "add other user as admin by owner" do
        other_user = FactoryBot.create(:user)
        @circle.add_user_as_admin(other_user.id)
        user_circle_permission_group = UserCirclePermissionGroup.where(user_id: other_user.id, circle_id: @circle.id, permission_group_id: @permission_group.id).first
        expect(user_circle_permission_group).to be_present
      end

      it "should not throw any error even though we try to add user as admin who is already admin" do
        other_user = FactoryBot.create(:user)
        @circle.add_user_as_admin(other_user.id)
        expect { @circle.add_user_as_admin(other_user.id) }.not_to raise_error
        user_circle_permission_group = UserCirclePermissionGroup.where(user_id: other_user.id, circle_id: @circle.id, permission_group_id: @permission_group.id).first
        expect(user_circle_permission_group).to be_present
      end

      it "remove other user as admin by owner" do
        other_user = FactoryBot.create(:user)
        @circle.add_user_as_admin(other_user.id)
        @circle.remove_user_as_admin(other_user.id)
        user_circle_permission_group = UserCirclePermissionGroup.where(user_id: other_user.id, circle_id: @circle.id, permission_group_id: @permission_group.id).first
        expect(user_circle_permission_group).not_to be_present
      end

      it "should not throw any error even though we try to remove user as admin who is not admin" do
        other_user = FactoryBot.create(:user)
        expect { @circle.remove_user_as_admin(other_user.id) }.not_to raise_error
      end

    end
  end

  describe "#get_webapp_mixpanel_embed_link" do
    context "get webapp mixpanel embed link" do
      it "returns webapp mixpanel embed link" do
        circle = FactoryBot.create(:circle)
        Metadatum.create!(entity: circle, key: "webapp_mixpanel_embed_link", value: "https://link.com")
        expect(circle.get_webapp_mixpanel_embed_link).to eq("https://link.com")
      end

      it "returns nil webapp mixpanel embed link" do
        circle = FactoryBot.create(:circle)
        expect(circle.get_webapp_mixpanel_embed_link).to be_nil
      end
    end
  end

  describe "#webapp_json" do
    context "get webapp json" do
      it "returns webapp json" do
        circle = FactoryBot.create(:circle)
        expect(circle.webapp_json).to eq({ id: circle.hashid, name: circle.name, photo_url: circle.photo&.url })
      end
    end
  end

  describe "#update_kyc_creative" do
    context "check whether update kyc creative callback is calling or not" do
      it "calls update kyc creative callback on name update for political leader level circle" do
        circle = FactoryBot.create(:circle, level: :political_leader,
                                   circle_photos:
                                     [FactoryBot.build(:circle_photo,
                                                       photo: FactoryBot.create(:photo),
                                                       photo_type: :poster,
                                                       photo_order: 1)])
        allow(GenerateKycCreativeImage).to receive(:perform_async)
        circle.update(name: "new name")
        expect(GenerateKycCreativeImage).to have_received(:perform_async).with(circle.id)
      end
      it "calls update kyc creative callback on photo update for political leader level circle" do
        circle = FactoryBot.create(:circle, level: :political_leader,
                                   circle_photos:
                                     [FactoryBot.build(:circle_photo,
                                                       photo: FactoryBot.create(:photo),
                                                       photo_type: :poster,
                                                       photo_order: 1)])
        allow(GenerateKycCreativeImage).to receive(:perform_async)
        circle.update(photo: FactoryBot.create(:photo))
        expect(GenerateKycCreativeImage).to have_received(:perform_async).with(circle.id)
      end
      it "does not call update kyc creative callback on name update for other than political leader level circle" do
        circle = FactoryBot.create(:circle, level: :state, circle_type: :location)
        allow(GenerateKycCreativeImage).to receive(:perform_async)
        circle.update(name: "new name")
        expect(GenerateKycCreativeImage).not_to have_received(:perform_async).with(circle.id)
      end
    end
  end

  describe "#suggesting_circles_of_user" do
    before :each do
      @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
      @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
      @district2 = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
      @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
      @mandal2 = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district2)
      @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)
      @party = FactoryBot.create(:circle)
      @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id, affiliated_party_circle_id: @party.id)

      @mp_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mp_constituency)
      @mla_constituency = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency, parent_circle: @mp_constituency)
      @mla_constituency2 = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency, parent_circle: @mp_constituency)
      @mla_constituency3 = FactoryBot.create(:circle, circle_type: :location, level: :mla_constituency, parent_circle: @mp_constituency)
      FactoryBot.create(:circles_relation, first_circle: @mandal, second_circle: @mla_constituency, relation: :Mandal2MLA)
      FactoryBot.create(:circles_relation, first_circle: @mandal2, second_circle: @mla_constituency2, relation: :Mandal2MLA)
      FactoryBot.create(:circles_relation, first_circle: @mandal, second_circle: @mla_constituency3, relation: :Mandal2MLA)

      @leader = FactoryBot.create(:circle, level: :political_leader, circle_photos: [FactoryBot.build(:circle_photo, photo: FactoryBot.create(:photo), photo_type: :poster, photo_order: 1)])
      @leader2 = FactoryBot.create(:circle, level: :political_leader, circle_photos: [FactoryBot.build(:circle_photo, photo: FactoryBot.create(:photo), photo_type: :poster, photo_order: 1)])
      @leader3 = FactoryBot.create(:circle, level: :political_leader, circle_photos: [FactoryBot.build(:circle_photo, photo: FactoryBot.create(:photo), photo_type: :poster, photo_order: 1)])
      @leader4 = FactoryBot.create(:circle, level: :political_leader, circle_photos: [FactoryBot.build(:circle_photo, photo: FactoryBot.create(:photo), photo_type: :poster, photo_order: 1)])
      @leader5 = FactoryBot.create(:circle, level: :political_leader, circle_photos: [FactoryBot.build(:circle_photo, photo: FactoryBot.create(:photo), photo_type: :poster, photo_order: 1)])
      @leader6 = FactoryBot.create(:circle, level: :political_leader, circle_photos: [FactoryBot.build(:circle_photo, photo: FactoryBot.create(:photo), photo_type: :poster, photo_order: 1)])
      @leader7 = FactoryBot.create(:circle, level: :political_leader, circle_photos: [FactoryBot.build(:circle_photo, photo: FactoryBot.create(:photo), photo_type: :poster, photo_order: 1)])
      @leader8 = FactoryBot.create(:circle, level: :political_leader, circle_photos: [FactoryBot.build(:circle_photo, photo: FactoryBot.create(:photo), photo_type: :poster, photo_order: 1)])

      FactoryBot.create(:circles_relation, first_circle_id: @mla_constituency.id, second_circle_id: @leader.id, relation: :MLA)
      FactoryBot.create(:circles_relation, first_circle_id: @mla_constituency3.id, second_circle_id: @leader2.id, relation: :MLA_Contestant)
      FactoryBot.create(:circles_relation, first_circle_id: @mla_constituency2.id, second_circle_id: @leader3.id, relation: :MLA)
      FactoryBot.create(:circles_relation, first_circle_id: @mp_constituency.id, second_circle_id: @leader4.id, relation: :MP)
      FactoryBot.create(:circles_relation, first_circle_id: @mp_constituency.id, second_circle_id: @leader6.id, relation: :MP_Contestant)
      FactoryBot.create(:circles_relation, first_circle_id: @leader7.id, second_circle_id: @mandal.id, relation: :Leader2Location)
      FactoryBot.create(:circles_relation, first_circle_id: @leader5.id, second_circle_id: @state.id, relation: :Leader2State)
      FactoryBot.create(:circles_relation, first_circle_id: @leader5.id, second_circle_id: @party.id, relation: :Leader2Party)
      FactoryBot.create(:circles_relation, first_circle_id: @leader8.id, second_circle_id: @state.id, relation: :Leader2State)
    end

    it "returns empty aray if user is not found" do
      data = Circle.suggesting_circles_of_user(0)
      expect(data).to eq([])
    end

    it "returns all the leader circle ids in close proximity to user district" do
      data = Circle.suggesting_circles_of_user(@user.id)
      expect(data).to be_present
      expect(data.length).to eq(5)
      expect(data[0][:id]).to eq(@leader.id)
      expect(data[1][:id]).to eq(@leader2.id)
      expect(data[2][:id]).to eq(@leader4.id)
      expect(data[3][:id]).to eq(@leader6.id)
      expect(data[4][:id]).to eq(@leader7.id)
    end
  end

  describe "#get_json_for_rm_layout_creation" do
    let(:circle) { create(:circle, name: "Test Circle", name_en: "Test Circle EN", short_name: "TC") }
    let(:poster_photo) { create(:circle_photo, photo_type: :poster, circle: circle) }

    it "returns JSON with poster photos when poster_photos_required is true" do
      poster_photo_json = {
        id: poster_photo.photo_id,
        url: poster_photo.photo.placeholder_url
      }
      allow(circle).to receive(:poster_photos).and_return([poster_photo])

      result = circle.get_json_for_rm_layout_creation(poster_photos_required: true)

      expect(result).to eq({
                             id: circle.id,
                             name: "Test Circle",
                             name_en: "Test Circle EN",
                             short_name: "TC",
                             poster_photos: [poster_photo_json],
                             sub_text: circle.get_sub_text
                           })
    end

    it "returns JSON without poster photos when poster_photos_required is false" do
      result = circle.get_json_for_rm_layout_creation(poster_photos_required: false)

      expect(result).to eq({
                             id: circle.id,
                             name: "Test Circle",
                             name_en: "Test Circle EN",
                             short_name: "TC",
                             poster_photos: [],
                             sub_text: circle.get_sub_text
                           })
    end

    it "returns JSON with nil short_name if short_name is not present" do
      circle.update(short_name: nil)

      result = circle.get_json_for_rm_layout_creation(poster_photos_required: true)

      expect(result).to eq({
                             id: circle.id,
                             name: "Test Circle",
                             name_en: "Test Circle EN",
                             short_name: nil,
                             poster_photos: [],
                             sub_text: circle.get_sub_text
                           })
    end
  end

  describe "#get_sub_text" do
    before :each do
      @circle = FactoryBot.create(:circle, level: :political_leader)
    end

    it "returns location and party names if both are present" do
      @location = FactoryBot.create(:circle, name: "Location Name", level: :state, circle_type: :location)
      @leader2location = FactoryBot.create(:circles_relation, first_circle: @circle, second_circle: @location,
                                           relation: :Leader2Location)
      @party = FactoryBot.create(:circle, name: "Party Name", level: :political_party)
      @leader2party = FactoryBot.create(:circles_relation, first_circle: @circle, second_circle: @party,
                                        relation: :Leader2Party)
      result = @circle.get_sub_text

      expect(result).to eq("Location Name , Party Name")
    end

    it "returns only location name if party is not present" do
      @location = FactoryBot.create(:circle, name: "Location Name", level: :state, circle_type: :location)
      @leader2location = FactoryBot.create(:circles_relation, first_circle: @circle, second_circle: @location,
                                           relation: :Leader2Location)
      result = @circle.get_sub_text

      expect(result).to eq("Location Name")
    end

    it "returns only party name if location is not present" do
      @party = FactoryBot.create(:circle, name: "Party Name", level: :political_party)
      @leader2party = FactoryBot.create(:circles_relation, first_circle: @circle, second_circle: @party,
                                        relation: :Leader2Party)
      result = @circle.get_sub_text

      expect(result).to eq("Party Name")
    end

    it "returns nil if neither location nor party is present" do
      result = @circle.get_sub_text

      expect(result).to be_nil
    end

    it "returns location name if circle type is political party" do
      @party = FactoryBot.create(:circle, name: "Party Name", level: :political_party)
      @location = FactoryBot.create(:circle, name: "Location Name", level: :state, circle_type: :location)
      FactoryBot.create(:circles_relation, first_circle: @party, second_circle: @location, relation: :Party2State)
      result = @party.get_sub_text
      expect(result).to eq("Location Name")
    end
  end

  describe ".get_default_leader_circle_ids_for_party" do
    it "returns mapped leader circle IDs for party ID 37788" do
      result = Circle.get_default_leader_circle_ids_for_party(37788)
      expect(result).to eq([38227, 38237, 32189, 38147, 33407, 38710, 38150, 38228])
    end

    it "returns mapped leader circle IDs for party ID 31398" do
      result = Circle.get_default_leader_circle_ids_for_party(31398)
      expect(result).to eq([38227, 38228, 38237, 31145, 31155, 31410, 31147, 31193, 32869])
    end

    it "returns mapped leader circle IDs for party ID 31405" do
      result = Circle.get_default_leader_circle_ids_for_party(31405)
      expect(result).to eq([31190, 31263, 31261, 32769, 31253, 32855])
    end

    it "returns empty array for unmapped party ID" do
      result = Circle.get_default_leader_circle_ids_for_party(999)
      expect(result).to eq([])
    end

    it "returns empty array for nil party ID" do
      result = Circle.get_default_leader_circle_ids_for_party(nil)
      expect(result).to eq([])
    end
  end

  describe "identity image generation trigger" do
    context "when circle name changes" do
      let(:circle) { FactoryBot.create(:circle, circle_type: :interest, level: :political_party) }
      let(:user) { FactoryBot.create(:user) }
      let(:role) { FactoryBot.create(:role) }
      let!(:user_role) { FactoryBot.create(:user_role, user: user, role: role, parent_circle_id: circle.id) }

      it "should trigger identity photo generation worker when circle name changes" do
        expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id).at_least(:once)

        circle.update!(name: "Updated Circle Name")
      end

      it "should trigger identity photo generation worker when circle short_name changes" do
        expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id).at_least(:once)

        circle.update!(short_name: "Updated Short Name")
      end

      it "should trigger identity photo generation worker for users with purview_circle_id" do
        # Skip this test for now as it requires complex circle hierarchy setup
        skip "Complex circle hierarchy setup required for purview testing"
      end

      it "should trigger identity photo generation worker for users with badge icons from this circle" do
        badge_icon_group = FactoryBot.create(:badge_icon_group, circle: circle)
        admin_user = FactoryBot.create(:admin_user)
        data = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        admin_medium = FactoryBot.create(:admin_medium, blob_data: data, admin_user: admin_user)
        badge_icon = FactoryBot.create(:badge_icon, badge_icon_group: badge_icon_group, admin_medium: admin_medium)

        # Create a different user to avoid duplicate user_role validation
        different_user = FactoryBot.create(:user)
        user_role_with_badge = FactoryBot.create(:user_role, user: different_user, role: role, badge_icon: badge_icon)

        # Expect the worker to be called for both the original user and the different user
        expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(user.id).at_least(:once)
        expect(IdentityPhotoGenerationWorker).to receive(:perform_async).with(different_user.id).at_least(:once)

        circle.update!(name: "Updated Circle Name")
      end

      it "should not trigger identity photo generation worker when unrelated attributes change" do
        allow(IdentityPhotoGenerationWorker).to receive(:perform_async)
        expect(IdentityPhotoGenerationWorker).not_to receive(:perform_async).with(user.id)

        circle.update!(active: false)
      end
    end
  end
end
